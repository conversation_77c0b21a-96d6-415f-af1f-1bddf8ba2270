import type { IVideo } from './interfaces/video';

export type IStorage = {
	videos: { [key: string]: IVideo };
	includedUrls: string[];
	isLight: boolean;
	rewindTime?: number; // in seconds
	forwardTime?: number; // in seconds
	alwaysLoop?: boolean;
	lastSync?: number;
	alwaysShuffle?: boolean;
	autoSkipAd?: boolean;
	// 新增保存路径配置
	downloadPath?: string; // 自定义下载路径，默认为系统下载文件夹
	createVideoFolder?: boolean; // 批量下载时是否创建视频文件夹，默认true
	// 浏览器和Cookie设置
	preferredBrowser?: string; // 用户偏好的浏览器
	enableCookiesByDefault?: boolean; // 默认启用cookies
	// 侧边栏设置
	preferSidebar?: boolean; // 用户是否偏好使用侧边栏模式，默认false
};

export const storageDriver = {
	get: async (): Promise<IStorage> => {
		try {
			const value = await chrome.storage.sync.get();
			const storage: IStorage = {
				videos: value.videos ?? {},
				includedUrls: value.includedUrls ?? [],
				isLight: value.isLight,
				rewindTime: value.rewindTime ?? 10,
				forwardTime: value.forwardTime ?? 10,

				lastSync: value.lastSync ?? 0,
				alwaysShuffle: value.alwaysShuffle,
				autoSkipAd: value.autoSkipAd ?? false,
				downloadPath: value.downloadPath ?? '', // 默认为空，使用系统下载文件夹
				createVideoFolder: value.createVideoFolder ?? true, // 默认创建视频文件夹
				preferredBrowser: value.preferredBrowser ?? '', // 默认为空
				enableCookiesByDefault: value.enableCookiesByDefault ?? false, // 默认不启用cookies
				preferSidebar: value.preferSidebar ?? false // 默认不使用侧边栏模式
			};
			return storage;
		} catch (error) {
			throw error;
		}
	},
	set: (value: IStorage): Promise<void> => {
		return chrome.storage.sync.set(value);
	},
};
