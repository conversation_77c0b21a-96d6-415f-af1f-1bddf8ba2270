import { storageDriver } from '../storage-driver';

chrome.commands.onCommand.addListener(async (command) => {
    if (command === 'play-toggle') {
        playToggle();
        return;
    }
});

// 监听存储变化，动态设置popup
chrome.storage.onChanged.addListener(async (changes, namespace) => {
    if (namespace === 'sync' && changes.preferSidebar) {
        await updateActionPopup();
    }
});

// 扩展启动时设置popup
chrome.runtime.onStartup.addListener(async () => {
    await updateActionPopup();
});

// 扩展安装时设置popup
chrome.runtime.onInstalled.addListener(async () => {
    await updateActionPopup();
});

// 动态更新action popup设置
async function updateActionPopup() {
    try {
        const storage = await storageDriver.get();

        // 如果用户偏好侧边栏，移除默认popup，让点击事件由onClicked处理
        if (storage.preferSidebar) {
            await chrome.action.setPopup({ popup: '' });
        } else {
            // 否则设置默认popup
            await chrome.action.setPopup({ popup: 'src/popup/index.html' });
        }
    } catch (error) {
        console.error('Error updating action popup:', error);
    }
}

// 处理扩展图标点击事件（仅在没有popup时触发）
chrome.action.onClicked.addListener(async (tab) => {
    try {
        // 获取用户设置
        const storage = await storageDriver.get();

        // 如果用户偏好侧边栏模式且当前标签页有效，打开侧边栏
        if (storage.preferSidebar && tab.id) {
            try {
                await chrome.sidePanel.open({ tabId: tab.id });
                return;
            } catch (error) {
                console.log('Failed to open sidebar:', error);
                // 如果侧边栏打开失败，可以考虑打开options页面或显示通知
                chrome.runtime.openOptionsPage();
            }
        }
    } catch (error) {
        console.error('Error handling action click:', error);
    }
});



async function playToggle() {
    const tabs = await chrome.tabs.query({});
    for (let i = 0; i < tabs.length; i++) {
        const tab = tabs[i];
        const isYoutubeMusic = !!tab.url?.includes('music.youtube.co');
        if (!isYoutubeMusic && !tab.url?.includes('youtube.com/watch?v=')) continue;

        if (isYoutubeMusic) {
            await chrome.scripting.executeScript({
                func: () => {
                    document.dispatchEvent(
                        new KeyboardEvent('keydown', {
                            key: ' ',
                            keyCode: 32,
                            which: 32,
                            shiftKey: false,
                            ctrlKey: false,
                            metaKey: false,
                        })
                    );
                },
                target: {
                    tabId: tab.id!,
                },
            });
            return;
        }

        await chrome.scripting.executeScript({
            func: () => {
                document.dispatchEvent(
                    new KeyboardEvent('keydown', {
                        key: 'k',
                        keyCode: 75,
                        code: 'KeyK',
                        which: 75,
                        shiftKey: false,
                        ctrlKey: false,
                        metaKey: false,
                    })
                );
            },
            target: {
                tabId: tab.id!,
            },
        });
        return;
    }
}

// 初始化popup设置
updateActionPopup();