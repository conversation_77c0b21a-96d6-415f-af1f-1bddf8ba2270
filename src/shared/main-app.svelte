<script lang="ts">
	import { onMount } from 'svelte';
	import CutIcon from '../assets/svg/cut-icon.svelte';
	import SettingIcon from '../assets/svg/setting-icon.svelte';
	import VolumeIcon from '../assets/svg/volume-icon.svelte';
	import { storageDriver, type IStorage } from '../storage-driver';
	import AudioController from '../popup/pages/audio-controller.svelte';
	import ClipperPage from '../popup/pages/clipper-page.svelte';
	import { storage } from '../popup/stores/storage';
	import MoonIcon from '../assets/svg/moon-icon.svelte';
	import SunIcon from '../assets/svg/sun-icon.svelte';
	import { onAuthStateChanged } from 'firebase/auth';
	import { auth, db } from '../popup/utils/firebase';
	import { authUser } from '../popup/stores/user-store';
	import ExitIcon from '../assets/svg/exit-icon.svelte';
	import { signOut } from 'firebase/auth';
	import { doc, setDoc, getDoc, type DocumentReference } from 'firebase/firestore';

	// 导出props以支持不同的容器模式
	export let isSidebar = false; // 是否为侧边栏模式

	let currentTab: 'clipper' | 'media' | 'records' = 'clipper';
	let isLight = false;
	let init = false;
	let VideoRecords: any = null;

	// 动态导入VideoRecords组件
	const loadVideoRecords = async () => {
		if (!VideoRecords) {
			try {
				const module = await import('../popup/pages/video-records.svelte');
				VideoRecords = module.default;
			} catch (error) {
				console.error('Failed to load VideoRecords component:', error);
			}
		}
	};

	// 防抖同步函数，避免频繁写入Firebase
	let syncTimeout: number | null = null;
	const sync = async (newStorage: IStorage) => {
		if (!$authUser) {
			return;
		}

		// 清除之前的定时器
		if (syncTimeout) {
			clearTimeout(syncTimeout);
		}

		// 设置新的定时器，延迟1秒后执行同步
		syncTimeout = setTimeout(async () => {
			try {
				const docRef = doc(db, 'clipper', $authUser.uid) as DocumentReference<IStorage>;
				await setDoc(docRef, newStorage);
				console.log('Firebase sync completed');
			} catch (error) {
				console.log('Firebase sync error:', error);
			}
			syncTimeout = null;
		}, 1000);
	};

	const updateTheme = () => {
		if (isLight) {
			document.documentElement.classList.remove('dark');
			// 侧边栏模式下也更新body类名
			if (isSidebar) {
				document.body.classList.remove('dark');
			}
		} else {
			document.documentElement.classList.add('dark');
			// 侧边栏模式下也更新body类名
			if (isSidebar) {
				document.body.classList.add('dark');
			}
		}
		if (init) {
			storage.update((prev) => {
				prev.isLight = isLight;
				return prev;
			});
		}
	};

	const logout = async () => {
		try {
			await signOut(auth);
		} catch (error) {
			console.error('Logout error:', error);
		}
	};

	// 监听存储变化并同步到Firebase
	$: $storage,
		(async () => {
			if (!init) {
				return;
			}
			await storageDriver.set($storage);
			sync($storage);
		})();

	$: isLight, updateTheme();

	onMount(async () => {
		const res = await storageDriver.get();
		storage.set(res);
		isLight = res.isLight;
		init = true;

		// 初始化主题
		updateTheme();

		// 如果用户偏好侧边栏模式且当前是popup模式，自动打开侧边栏
		if (!isSidebar && res.preferSidebar) {
			try {
				const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
				if (tab.id) {
					await chrome.sidePanel.open({ tabId: tab.id });
					// 关闭popup（如果可能的话）
					window.close();
					return;
				}
			} catch (error) {
				console.log('Failed to auto-open sidebar:', error);
			}
		}

		// 获取当前标签页URL
		const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

		// 检测是否为受支持的网站（优先检测YouTube，然后检查用户配置的URL）
		let isSupportedSite = false;
		if (tab.url) {
			// 首先检测是否为YouTube网站
			const isYouTubeSite = tab.url.includes('youtube.com') || tab.url.includes('youtu.be');

			// 然后检查用户配置的URL模式
			const isConfiguredSite = res.includedUrls.some((pattern: string) => {
				try {
					return new RegExp(pattern).test(tab.url);
				} catch {
					return false;
				}
			});

			isSupportedSite = isYouTubeSite || isConfiguredSite;
		}

		// 检查当前页面是否在记录中
		let isInRecords = false;
		if (tab.url && tab.url.includes('youtube.com/watch')) {
			const regex = /[?&]v=([^&#]+)/;
			const match = tab.url.match(regex);
			const videoId = match && match[1];
			if (videoId && res.videos[videoId]) {
				isInRecords = true;
			}
		}

		// 设置默认页面逻辑：
		// 1. 先检测当前处于的页面是否在记录中，如果是的话，优先打开记录界面
		// 2. 当检测到处于受支持的网页，但是不在记录中，默认打开剪藏页面
		// 3. 其余页面默认打开媒体页面
		if (isInRecords) {
			currentTab = 'records';
		} else if (isSupportedSite) {
			currentTab = 'clipper';
		} else {
			currentTab = 'media';
		}

		// 监听来自records页面的切换到clipper页面的事件
		window.addEventListener('switchToClipper', () => {
			currentTab = 'clipper';
		});

		// 监听来自clipper页面的切换回records页面的事件
		window.addEventListener('switchToRecords', () => {
			currentTab = 'records';
		});
	});

	// Firebase认证状态监听
	onMount(() => {
		const unsubscribe = onAuthStateChanged(auth, async (user) => {
			if (user) {
				authUser.set(user);
				// 从Firebase加载数据
				try {
					const docRef = doc(db, 'clipper', user.uid) as DocumentReference<IStorage>;
					const docSnap = await getDoc(docRef);
					if (docSnap.exists()) {
						const firebaseData = docSnap.data();
						// 合并本地数据和Firebase数据
						storage.update((localData) => {
							return {
								...localData,
								...firebaseData,
								lastSync: new Date().getTime()
							};
						});
					}
				} catch (error) {
					console.log('Firebase load error:', error);
				}
			} else {
				authUser.set(null);
			}
		});

		return unsubscribe;
	});

	// 打开设置页面
	const openSettings = () => {
		chrome.runtime.openOptionsPage();
	};

	// 打开侧边栏（仅在popup模式下显示）
	const openSidebar = async () => {
		try {
			const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
			if (tab.id) {
				await chrome.sidePanel.open({ tabId: tab.id });
			}
		} catch (error) {
			console.error('Failed to open sidebar:', error);
		}
	};
</script>

<!-- 动态容器类，根据模式调整宽度和高度 -->
<main class="{isSidebar ? 'w-[550px] min-h-screen bg-light dark:bg-dark' : 'min-w-[550px] max-h-[600px] bg-light dark:bg-dark'} p-4 flex flex-col items-center text-dark dark:text-light overflow-y-auto">
	<div class="flex items-center gap-3 w-full p-2 bg-gray-100 dark:bg-gray-800 rounded-xl shadow-inner">
		<!-- 主题切换按钮 - 轻拟物风格 -->
		<button
			on:click={() => {
				isLight = !isLight;
			}}
			class="{isSidebar ? 'p-3' : 'p-2'} rounded-lg bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800
				   shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200
				   fill-yellow-500 dark:fill-yellow-400 hover:fill-yellow-600 dark:hover:fill-yellow-300
				   active:shadow-inner active:transform-none"
			title={isLight ? '切换到深色模式' : '切换到浅色模式'}
		>
			{#if isLight}
				<MoonIcon width={isSidebar ? 20 : 18} />
			{:else}
				<SunIcon width={isSidebar ? 20 : 18} />
			{/if}
		</button>

		<!-- 导航按钮组 -->
		<div class="flex {isSidebar ? 'gap-3' : 'gap-2'} flex-1 justify-center">
			<!-- 剪藏页面按钮 -->
			<button
				on:click={() => {
					currentTab = 'clipper';
					loadVideoRecords();
				}}
				class="{isSidebar ? 'p-3' : 'p-2'} rounded-lg bg-gradient-to-br transition-all duration-200
					   {currentTab === 'clipper'
						? 'from-blue-300 to-blue-400 dark:from-blue-600 dark:to-blue-700 shadow-inner'
						: 'from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'}
					   fill-blue-600 dark:fill-blue-400 hover:fill-blue-700 dark:hover:fill-blue-300
					   active:shadow-inner active:transform-none"
				title="剪藏页面"
			>
				<CutIcon width={isSidebar ? 20 : 18} />
			</button>

			<!-- 媒体控制按钮 -->
			<button
				on:click={() => {
					currentTab = 'media';
				}}
				class="{isSidebar ? 'p-3' : 'p-2'} rounded-lg bg-gradient-to-br transition-all duration-200
					   {currentTab === 'media'
						? 'from-green-300 to-green-400 dark:from-green-600 dark:to-green-700 shadow-inner'
						: 'from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'}
					   fill-green-600 dark:fill-green-400 hover:fill-green-700 dark:hover:fill-green-300
					   active:shadow-inner active:transform-none"
				title="媒体控制"
			>
				<VolumeIcon width={isSidebar ? 20 : 18} />
			</button>

			<!-- 记录页面按钮 -->
			<button
				on:click={() => {
					currentTab = 'records';
					loadVideoRecords();
				}}
				class="{isSidebar ? 'p-3' : 'p-2'} rounded-lg bg-gradient-to-br transition-all duration-200
					   {currentTab === 'records'
						? 'from-purple-300 to-purple-400 dark:from-purple-600 dark:to-purple-700 shadow-inner'
						: 'from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'}
					   fill-purple-600 dark:fill-purple-400 hover:fill-purple-700 dark:hover:fill-purple-300
					   active:shadow-inner active:transform-none"
				title="视频记录"
			>
				<svg width={isSidebar ? 20 : 18} height={isSidebar ? 20 : 18} viewBox="0 0 24 24">
					<path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
				</svg>
			</button>
		</div>

		<!-- 右侧按钮组 -->
		<div class="flex {isSidebar ? 'gap-3' : 'gap-2'}">
			<!-- 设置按钮 -->
			<button
				on:click={openSettings}
				class="{isSidebar ? 'p-3' : 'p-2'} rounded-lg bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800
					   shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200
					   fill-gray-600 dark:fill-gray-400 hover:fill-gray-700 dark:hover:fill-gray-300
					   active:shadow-inner active:transform-none"
				title="设置"
			>
				<SettingIcon width={isSidebar ? 20 : 18} />
			</button>

			<!-- 侧边栏按钮（仅在popup模式下显示） -->
			{#if !isSidebar}
				<button
					on:click={openSidebar}
					class="p-2 rounded-lg bg-gradient-to-br from-indigo-200 to-indigo-300 dark:from-indigo-700 dark:to-indigo-800
						   shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200
						   fill-indigo-600 dark:fill-indigo-400 hover:fill-indigo-700 dark:hover:fill-indigo-300
						   active:shadow-inner active:transform-none"
					title="打开侧边栏"
				>
					<svg width="18" height="18" viewBox="0 0 24 24">
						<path d="M3 3h6v18H3V3zm8 0h10v18H11V3z"/>
					</svg>
				</button>
			{/if}

			<!-- 登出按钮（仅在已登录时显示） -->
			{#if $authUser}
				<button
					on:click={logout}
					class="{isSidebar ? 'p-3' : 'p-2'} rounded-lg bg-gradient-to-br from-red-200 to-red-300 dark:from-red-700 dark:to-red-800
						   shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200
						   fill-red-600 dark:fill-red-400 hover:fill-red-700 dark:hover:fill-red-300
						   active:shadow-inner active:transform-none"
				>
					<ExitIcon width={isSidebar ? 20 : 18} height={isSidebar ? 20 : 18} />
				</button>
			{/if}
		</div>
	</div>
	<!-- 轻拟物风格分割线 -->
	<div class="w-full mt-3 mb-3 px-2">
		<div class="h-[1px] bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent shadow-sm" />
	</div>
	{#if currentTab === 'clipper'}
		<ClipperPage />
	{:else if currentTab === 'media'}
		<AudioController />
	{:else if currentTab === 'records'}
		{#if VideoRecords}
			<svelte:component this={VideoRecords} />
		{:else}
			<div class="flex items-center justify-center h-32">
				<div class="text-gray-500 dark:text-gray-400">加载中...</div>
			</div>
		{/if}
	{/if}
</main>
